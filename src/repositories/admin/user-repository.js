const cosmosDbContext = require('../../cosmosDbContext/comosdb-context')
const userContainer = 'Users'

class UserRepository {
  async createSuperAdmin(data) {
    return await cosmosDbContext.createItem(data, userContainer)
  }

  async createOrganizationAdmin(data) {
    return cosmosDbContext.createItem(data, userContainer)
  }

  async getUserByRole(role) {
    const query = `SELECT * FROM c WHERE c.userRole = '${role}'`
    const a = cosmosDbContext.queryItems(query, userContainer)
    return a
  }

  async getUserByEmail(email) {
    const query = `SELECT * FROM c WHERE c.email = '${email}'`
    return cosmosDbContext.queryItems(query, userContainer)
  }

  async getUsersByOrganizationId(organizationId) {
    const query = `SELECT * FROM c WHERE c.organizationId = "${organizationId}" AND c.isActive = true`
    return cosmosDbContext.queryItems(query, 'Users')
  }

  // async updatePassword(userId, hashedPassword) {
  //   const query = `SELECT * FROM c WHERE c.id = '${userId}'`
  //   const users = await cosmosDbContext.queryItems(query, userContainer)

  //   if (users && users.length > 0) {
  //     const user = users[0]
  //     user.password = hashedPassword
  //     user.resetToken = null // Clear the reset token
  //     user.resetTokenExpiry = null

  //     return cosmosDbContext.upsertItem(user.id, user, userContainer)
  //   }

  //   return null
  // }

  async updateUser(userId, userData) {
    return cosmosDbContext.upsertItem(userId, userData, userContainer)
  }

  async getAllUsers() {
    const query = 'SELECT * FROM c'
    return cosmosDbContext.queryItems(query, userContainer)
  }

  async getUsersByQuery(query, pageSize = null, continueToken = null) {
    if (pageSize) {
      const result = await cosmosDbContext.getAllItemQuery(userContainer, query, pageSize, continueToken)
      return result.items
    }
    return cosmosDbContext.queryItems(query, userContainer)
  }

  async getUsersCount(countQuery) {
    const result = await cosmosDbContext.queryItems(countQuery, userContainer)
    return result?.[0] || 0
  }
}

module.exports = new UserRepository()
