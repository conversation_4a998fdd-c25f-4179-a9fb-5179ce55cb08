const getAllOrganizationsQuery = (nameFilter) => {
  let query = 'SELECT * FROM c'
  if (nameFilter) {
    query += ` WHERE STARTSWITH(LOWER(c.name), LOWER("${nameFilter}"))`
  }
  query += ' ORDER BY c._ts DESC'
  return query
}

const getOrganizationByIdQuery = (id) => {
  return `SELECT * FROM c WHERE c.id = "${id}"`
}

const getOrganizationByNameQuery = (name) => {
  return `SELECT * FROM c WHERE c.name = "${name}"`
}

const getOrganizationByEmailQuery = (email) => {
  return `SELECT * FROM c WHERE c.contactEmail = "${email}"`
}

const getOrganizationsByIdsQuery = (ids) => {
  return {
    query: `SELECT * FROM c WHERE ARRAY_CONTAINS(@ids, c.id)`,
    parameters: [{ name: '@ids', value: ids }],
  }
}

module.exports = {
  getAllOrganizationsQuery,
  getOrganizationByIdQuery,
  getOrganizationByNameQuery,
  getOrganizationByEmailQuery,
  getOrganizationsByIdsQuery,
}
