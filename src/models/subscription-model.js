const { v4: uuidv4 } = require('uuid')
const CosmosDbMetadata = require('./CosmosDb-Metadata-model')

// Special Organization Plan ID - should be hidden from GET endpoints
const ORGANIZATION_PLAN_ID = 'organization-plan-settings'

// Module types that can be included in subscription plans
const MODULES = {
  MRD: 'MRD',
  EMR: 'EMR',
  BILLING: 'Billing'
}

// Available sub-features under each module
const MODULE_FEATURES = {
  MRD: [
    'Patient Registration',
    'Patient Queue Management',
    'Administrative Data',
    'Records Management'
  ],
  EMR: [
    'Ambient Listening',
    'Patient Information',
    'Consultation',
    'Prescription',
    'Lab Master',
    'Lifestyle Module',
    'Doctor Profile',
    'Patient History',
    'Vitals Tracking'
  ],
  Billing: [
    'Payment Orders',
    'Payment Verification',
    'Payment Reports',
    'Invoice Generation',
    'Payment Statistics'
  ]
}

class SubscriptionPlan extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || uuidv4()
    this.planName = data.planName || ''
    this.description = data.description || ''
    this.validity = data.validity || 'Both' // "Monthly", "Yearly", or "Both"

    // Module-based features with feature IDs and pricing
    this.features = {
      MRD: data.features?.MRD || [], // [{ featureId: "uuid", monthlyAmount: 500, yearlyAmount: 5000 }]
      EMR: data.features?.EMR || [],
      Billing: data.features?.Billing || []
    }

    this.addOnFeatures = {
      MRD: data.addOnFeatures?.MRD || [],
      EMR: data.addOnFeatures?.EMR || [],
      Billing: data.addOnFeatures?.Billing || []
    }

    this.isActive = data.isActive !== undefined ? data.isActive : true
  }

  validate() {
    const errors = []

    if (!this.planName || this.planName.trim() === '') {
      errors.push('Plan name is required')
    }

    if (!this.validity || !['Monthly', 'Yearly', 'Both'].includes(this.validity)) {
      errors.push('Validity must be Monthly, Yearly, or Both')
    }

    // Validate base features for each module
    Object.keys(this.features).forEach(module => {
      if (!Array.isArray(this.features[module])) {
        errors.push(`${module} features must be an array`)
      } else {
        this.features[module].forEach((feature, index) => {
          if (!feature.featureId) {
            errors.push(`${module} feature at index ${index} must have a featureId`)
          }
          if (typeof feature.monthlyAmount !== 'number' || feature.monthlyAmount < 0) {
            errors.push(`${module} feature at index ${index} must have a valid monthlyAmount >= 0`)
          }
          if (typeof feature.yearlyAmount !== 'number' || feature.yearlyAmount < 0) {
            errors.push(`${module} feature at index ${index} must have a valid yearlyAmount >= 0`)
          }
        })
      }
    })

    // Validate add-on features for each module
    Object.keys(this.addOnFeatures).forEach(module => {
      if (!Array.isArray(this.addOnFeatures[module])) {
        errors.push(`${module} add-on features must be an array`)
      } else {
        this.addOnFeatures[module].forEach((feature, index) => {
          if (!feature.featureId) {
            errors.push(`${module} add-on feature at index ${index} must have a featureId`)
          }
          if (typeof feature.monthlyAmount !== 'number' || feature.monthlyAmount < 0) {
            errors.push(`${module} add-on feature at index ${index} must have a valid monthlyAmount >= 0`)
          }
          if (typeof feature.yearlyAmount !== 'number' || feature.yearlyAmount < 0) {
            errors.push(`${module} add-on feature at index ${index} must have a valid yearlyAmount >= 0`)
          }
        })
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  calculateBasicAmount(billingType = 'yearly') {
    let total = 0

    Object.values(this.features).forEach(featureArray => {
      featureArray.forEach(feature => {
        if (billingType === 'monthly') {
          total += feature.monthlyAmount || 0
        } else {
          total += feature.yearlyAmount || 0
        }
      })
    })

    return total
  }

  calculateTotalAmount(billingType = 'yearly') {
    // billingType: 'monthly' or 'yearly'
    let total = 0

    // Calculate from base features
    Object.values(this.features).forEach(featureArray => {
      featureArray.forEach(feature => {
        if (billingType === 'monthly') {
          total += feature.monthlyAmount || 0
        } else {
          total += feature.yearlyAmount || 0
        }
      })
    })

    // Calculate from add-on features
    Object.values(this.addOnFeatures).forEach(featureArray => {
      featureArray.forEach(feature => {
        if (billingType === 'monthly') {
          total += feature.monthlyAmount || 0
        } else {
          total += feature.yearlyAmount || 0
        }
      })
    })

    return total
  }

  // Check if a module has any features
  hasModule(moduleName) {
    const baseFeatures = this.features[moduleName] || []
    return baseFeatures.length > 0
  }

  // Get list of modules that have features
  getIncludedModules() {
    return Object.keys(this.features).filter(module => this.hasModule(module))
  }

  // Get all feature IDs for a module
  getModuleFeatureIds(moduleName) {
    const baseIds = (this.features[moduleName] || []).map(f => f.featureId)
    const addOnIds = (this.addOnFeatures[moduleName] || []).map(f => f.featureId)
    return { baseIds, addOnIds }
  }

  // Get all feature IDs across all modules
  getAllFeatureIds() {
    const allIds = []
    Object.values(this.features).forEach(featureArray => {
      featureArray.forEach(f => allIds.push(f.featureId))
    })
    Object.values(this.addOnFeatures).forEach(featureArray => {
      featureArray.forEach(f => allIds.push(f.featureId))
    })
    return allIds
  }

  toJSON() {
    return {
      id: this.id,
      planName: this.planName,
      description: this.description,
      validity: this.validity,
      features: this.features,
      addOnFeatures: this.addOnFeatures,
      includedModules: this.getIncludedModules(),
      monthlyTotal: this.calculateTotalAmount('monthly'),
      yearlyTotal: this.calculateTotalAmount('yearly'),
      totalMonthlyBasicAmount: this.calculateBasicAmount('monthly'),
      totalYearlyBasicAmount: this.calculateBasicAmount('yearly'),
      isActive: this.isActive,
      created_by: this.created_by,
      created_on: this.created_on,
      updated_on: this.updated_on,
      updated_by: this.updated_by,
    }
  }
}

class OrganizationSubscription extends CosmosDbMetadata {
  constructor(data) {
    super(data)
    this.id = data.id || uuidv4()
    this.organizationId = data.organizationId || ''
    this.planId = data.planId || ''
    this.planName = data.planName || ''
    this.validity = data.validity || 'Both'
    this.billingType = data.billingType || 'yearly' // 'monthly' or 'yearly' - what customer chose

    // Copy features from plan (snapshot at subscription time) - module-based
    this.features = data.features || {
      MRD: [],
      EMR: [],
      Billing: []
    }

    this.addOnFeatures = data.addOnFeatures || {
      MRD: [],
      EMR: [],
      Billing: []
    }

    this.totalAmount = data.totalAmount || 0
    this.status = data.status || 'active' // active, free trial, pending , expired, cancelled
    this.subscriptionType = data.subscriptionType || 'organization' // 'organization' or 'clinic' - how subscription was created
    this.isFreeTrial = data.isFreeTrial !== undefined ? data.isFreeTrial : false // Indicates if this is a free trial
    this.trialUsed = data.trialUsed !== undefined ? data.trialUsed : false // Tracks if user has used their trial
    this.userCreated = data.userCreated !== undefined ? data.userCreated : false // Tracks if user account was created
    this.startDate = data.startDate || new Date().toISOString()
    this.endDate = data.endDate || this.calculateEndDate(data.startDate, data.billingType)
    this.autoRenew = data.autoRenew !== undefined ? data.autoRenew : false
    this.paymentMethod = data.paymentMethod || 'razorpay'
    this.paymentId = data.paymentId || null

    // Billing details
    this.billingDetails = data.billingDetails || {
      contactName: data.billingDetails?.contactName || '',
      email: data.billingDetails?.email || '',
      pincode: data.billingDetails?.pincode || ''
    }

    // Contact email for clinic flow (to send activation email)
    this.contactEmail = data.contactEmail || data.billingDetails?.email || ''
  }

  calculateEndDate(startDate, billingType) {
    const start = startDate ? new Date(startDate) : new Date()
    const end = new Date(start)

    if (billingType === 'monthly') {
      end.setMonth(end.getMonth() + 1)
    } else {
      // Default to yearly
      end.setFullYear(end.getFullYear() + 1)
    }

    return end.toISOString()
  }

  // Check if organization has access to a specific module
  hasModuleAccess(moduleName) {
    const features = this.features[moduleName] || []
    return features.length > 0
  }

  // Check if organization has access to a specific feature by ID
  hasFeatureAccess(featureId) {
    const allIds = this.getAllFeatureIds()
    return allIds.includes(featureId)
  }

  // Get list of accessible modules
  getAccessibleModules() {
    return Object.keys(this.features).filter(module => this.hasModuleAccess(module))
  }

  // Get all feature IDs for a module
  getModuleFeatureIds(moduleName) {
    const baseIds = (this.features[moduleName] || []).map(f => f.featureId)
    const addOnIds = (this.addOnFeatures[moduleName] || []).map(f => f.featureId)
    return { baseIds, addOnIds }
  }

  // Get all feature IDs across all modules
  getAllFeatureIds() {
    const allIds = []
    Object.values(this.features).forEach(featureArray => {
      featureArray.forEach(f => allIds.push(f.featureId))
    })
    Object.values(this.addOnFeatures).forEach(featureArray => {
      featureArray.forEach(f => allIds.push(f.featureId))
    })
    return allIds
  }

  validate() {
    const errors = []

    if (!this.organizationId || this.organizationId.trim() === '') {
      errors.push('Organization ID is required')
    }

    if (!this.planId || this.planId.trim() === '') {
      errors.push('Plan ID is required')
    }

    if (!['active', 'free trial', 'expired', 'cancelled', 'pending'].includes(this.status)) {
      errors.push('Status must be active, free trial, expired, cancelled, or pending')
    }

    if (!['Monthly', 'Yearly', 'Both'].includes(this.validity)) {
      errors.push('Validity must be Monthly, Yearly, or Both')
    }

    if (!['monthly', 'yearly'].includes(this.billingType)) {
      errors.push('Billing type must be monthly or yearly')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  toJSON() {
    return {
      id: this.id,
      organizationId: this.organizationId,
      planId: this.planId,
      planName: this.planName,
      validity: this.validity,
      billingType: this.billingType,
      features: this.features,
      addOnFeatures: this.addOnFeatures,
      accessibleModules: this.getAccessibleModules(),
      totalAmount: this.totalAmount,
      status: this.status,
      subscriptionType: this.subscriptionType,
      isFreeTrial: this.isFreeTrial,
      trialUsed: this.trialUsed,
      userCreated: this.userCreated,
      startDate: this.startDate,
      endDate: this.endDate,
      autoRenew: this.autoRenew,
      paymentMethod: this.paymentMethod,
      paymentId: this.paymentId,
      contactEmail: this.contactEmail,
      created_by: this.created_by,
      created_on: this.created_on,
      updated_on: this.updated_on,
      updated_by: this.updated_by,
    }
  }
}

module.exports = {
  SubscriptionPlan,
  OrganizationSubscription,
  MODULES,
  MODULE_FEATURES,
  ORGANIZATION_PLAN_ID,
}
