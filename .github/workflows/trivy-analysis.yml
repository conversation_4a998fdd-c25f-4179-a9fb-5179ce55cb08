name: Trivy Security Scan

on:
  push:
    branches: [ "main", "staging", "develop" ]
  pull_request:
    branches: [ "main", "staging", "develop" ]
  workflow_dispatch:

permissions:
  contents: read
  security-events: write # For uploading SARIF results

jobs:
  trivy-scan:
    name: Build and Scan
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set Environment Variables
        env:
          ACR_LOGIN_SERVER_DEV: ${{ secrets.ACR_LOGIN_SERVER_DEV }}
          ACR_USERNAME_DEV: ${{ secrets.ACR_USERNAME_DEV }}
          ACR_PASSWORD_DEV: ${{ secrets.ACR_PASSWORD_DEV }}
          ACR_LOGIN_SERVER_STAGING: ${{ secrets.ACR_LOGIN_SERVER_STAGING }}
          ACR_USERNAME_STAGING: ${{ secrets.ACR_USERNAME_STAGING }}
          ACR_PASSWORD_STAGING: ${{ secrets.ACR_PASSWORD_STAGING }}
        run: |
          if [[ "${{ github.ref_name }}" == "staging" ]]; then
            echo "ACR_LOGIN_SERVER=$ACR_LOGIN_SERVER_STAGING" >> $GITHUB_ENV
            echo "ACR_USERNAME=$ACR_USERNAME_STAGING" >> $GITHUB_ENV
            echo "ACR_PASSWORD=$ACR_PASSWORD_STAGING" >> $GITHUB_ENV
          else
            echo "ACR_LOGIN_SERVER=$ACR_LOGIN_SERVER_DEV" >> $GITHUB_ENV
            echo "ACR_USERNAME=$ACR_USERNAME_DEV" >> $GITHUB_ENV
            echo "ACR_PASSWORD=$ACR_PASSWORD_DEV" >> $GITHUB_ENV
          fi

      - name: Log in to Azure Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.ACR_LOGIN_SERVER }}
          username: ${{ env.ACR_USERNAME }}
          password: ${{ env.ACR_PASSWORD }}

      - name: Build Docker Image
        run: |
          IMAGE_TAG=${{ env.ACR_LOGIN_SERVER }}/emr-ms:${{ github.ref_name }}-${{ github.sha }}
          docker build -t $IMAGE_TAG .
          echo "IMAGE_TAG=$IMAGE_TAG" >> $GITHUB_ENV

      - name: Run Trivy Vulnerability Scanner (Filesystem)
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-fs-results.sarif'
          severity: 'CRITICAL,HIGH'

      - name: Run Trivy SBOM Generation
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          format: 'cyclonedx'
          output: 'sbom-${{ github.sha }}.json'

      - name: Run Trivy Vulnerability Scanner (Image)
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'image'
          image-ref: ${{ env.IMAGE_TAG }}
          format: 'sarif'
          output: 'trivy-image-results.sarif'
          severity: 'CRITICAL,HIGH'
          ignore-unfixed: true

      - name: Upload Trivy Scan Results (FS)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-fs-results.sarif'
          category: 'trivy-fs'

      - name: Upload Trivy Scan Results (Image)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-image-results.sarif'
          category: 'trivy-image'

      - name: Upload SBOM Artifact
        uses: actions/upload-artifact@v4
        with:
          name: sbom-${{ github.sha }}
          path: sbom-${{ github.sha }}.json
          retention-days: 90
